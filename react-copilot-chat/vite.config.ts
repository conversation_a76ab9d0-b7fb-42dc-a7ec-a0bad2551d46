import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/theme': path.resolve(__dirname, './src/theme'),
      '@/types': path.resolve(__dirname, './src/types'),
    },
  },
  server: {
    hmr: {
      port: 5174, // Use a different port for HMR to avoid conflicts
      overlay: false, // Disable error overlay that might interfere
    },
    // Ensure CORS is properly configured for external API calls
    cors: true,
  },
})
