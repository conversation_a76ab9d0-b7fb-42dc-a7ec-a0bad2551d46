import React, { useState } from 'react'
import { testDirectLineConnection, runConnectivityTests } from '../utils/directLineTest'
import { validateTokenFormat } from '../utils/tokenValidator'

/**
 * DirectLineTestComponent - Standalone component for testing Direct Line connectivity
 * Helps diagnose connection issues without the full WebChat interface
 */
const DirectLineTestComponent: React.FC = () => {
  const [testResults, setTestResults] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')

  const envToken = import.meta.env.VITE_DIRECT_LINE_TOKEN

  const runTests = async () => {
    setIsLoading(true)
    setError('')
    setTestResults(null)

    try {
      if (!envToken || envToken === 'your_direct_line_token_here') {
        setError('No Direct Line token configured in environment variables')
        return
      }

      console.log('🧪 Starting Direct Line tests...')
      
      // Validate token format first
      const formatValidation = validateTokenFormat(envToken)
      console.log('📝 Token format validation:', formatValidation)

      if (!formatValidation.isValid) {
        setError(`Token format invalid: ${formatValidation.error}`)
        return
      }

      // Run connectivity tests
      const results = await runConnectivityTests(envToken)
      console.log('📊 Test results:', results)
      setTestResults(results)

      if (!results.directLine.success) {
        setError(`Direct Line test failed: ${results.directLine.error}`)
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      console.error('❌ Test error:', errorMessage)
      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const testDirectConnection = async () => {
    setIsLoading(true)
    setError('')

    try {
      if (!envToken) {
        setError('No token available')
        return
      }

      const result = await testDirectLineConnection(envToken)
      console.log('🔗 Direct connection test:', result)
      
      if (result.success) {
        alert(`✅ Direct Line connection successful!\nConversation ID: ${result.details?.conversationId}`)
      } else {
        alert(`❌ Direct Line connection failed:\n${result.error}`)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      alert(`❌ Test error: ${errorMessage}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div style={{
      padding: '20px',
      maxWidth: '800px',
      margin: '0 auto',
      fontFamily: 'system-ui, sans-serif'
    }}>
      <h2>🧪 Direct Line Connection Test</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <p><strong>Environment Token:</strong> {envToken ? `${envToken.substring(0, 20)}...` : 'Not configured'}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={runTests}
          disabled={isLoading}
          style={{
            padding: '10px 20px',
            marginRight: '10px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? '🔄 Testing...' : '🧪 Run Full Tests'}
        </button>

        <button
          onClick={testDirectConnection}
          disabled={isLoading}
          style={{
            padding: '10px 20px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? '🔄 Testing...' : '🔗 Test Direct Connection'}
        </button>
      </div>

      {error && (
        <div style={{
          padding: '15px',
          backgroundColor: '#f8d7da',
          color: '#721c24',
          border: '1px solid #f5c6cb',
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <strong>❌ Error:</strong> {error}
        </div>
      )}

      {testResults && (
        <div style={{
          padding: '15px',
          backgroundColor: testResults.directLine.success ? '#d4edda' : '#f8d7da',
          color: testResults.directLine.success ? '#155724' : '#721c24',
          border: `1px solid ${testResults.directLine.success ? '#c3e6cb' : '#f5c6cb'}`,
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          <h3>📊 Test Results</h3>
          <p><strong>Summary:</strong> {testResults.summary}</p>
          <p><strong>Direct Line:</strong> {testResults.directLine.success ? '✅ Success' : '❌ Failed'}</p>
          <p><strong>WebSocket:</strong> {testResults.webSocket ? '✅ Available' : '⚠️ Issues detected'}</p>
          
          {testResults.directLine.details && (
            <div style={{ marginTop: '10px' }}>
              <h4>🔍 Details:</h4>
              <pre style={{
                backgroundColor: 'rgba(0,0,0,0.1)',
                padding: '10px',
                borderRadius: '4px',
                fontSize: '12px',
                overflow: 'auto'
              }}>
                {JSON.stringify(testResults.directLine.details, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}

      <div style={{
        padding: '15px',
        backgroundColor: '#e2e3e5',
        borderRadius: '4px',
        fontSize: '14px'
      }}>
        <h4>💡 About this test:</h4>
        <ul>
          <li>Tests Direct Line token format and connectivity</li>
          <li>Checks WebSocket availability (separate from Direct Line)</li>
          <li>The WebSocket error to localhost:5173 is likely from Vite HMR, not Direct Line</li>
          <li>Direct Line uses HTTPS endpoints, not WebSocket to localhost</li>
        </ul>
      </div>
    </div>
  )
}

export default DirectLineTestComponent
