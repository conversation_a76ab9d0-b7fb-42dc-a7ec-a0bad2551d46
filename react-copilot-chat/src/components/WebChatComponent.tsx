import React, { useState, useEffect } from 'react'
import React<PERSON>eb<PERSON><PERSON>, { createDirectLine, createStyleSet } from 'botframework-webchat'
import { Card, StatusIndicator, LoadingIndicator, ErrorIndicator } from '@/components/ui'
import { theme, createWebChatStyleSet } from '@/theme'
import { DirectLineOptions } from '@/types'
import { validateTokenFormat, getTokenTroubleshootingSteps } from '../utils/tokenValidator'

interface WebChatComponentProps {
  /** Whether the chat is embedded in a floating window */
  isFloating?: boolean
  /** Callback when close button is clicked (for floating mode) */
  onClose?: () => void
}

/**
 * WebChatComponent - Main component for Microsoft Bot Framework WebChat integration
 * Handles DirectLine connection, error states, and provides a secure chat interface
 * Only works with environment-configured Direct Line tokens
 */
const WebChatComponent: React.FC<WebChatComponentProps> = ({
  isFloating = false,
  onClose
}) => {
  // Get configuration from environment variables
  const envToken = import.meta.env.VITE_DIRECT_LINE_TOKEN
  const botName = import.meta.env.VITE_BOT_NAME || 'Copilot Assistant'

  // Component state
  const [directLine, setDirectLine] = useState<unknown | null>(null)
  const [error, setError] = useState<string>('')
  const [isInitializing, setIsInitializing] = useState<boolean>(true)
  const [connectionStatus, setConnectionStatus] = useState<string>('Initializing')
  const [retryCount, setRetryCount] = useState<number>(0)

  // Initialize DirectLine connection on component mount
  useEffect(() => {
    const initializeWebChat = async (): Promise<void> => {
      console.log('🚀 Initializing WebChat...', { isFloating, envToken: envToken ? 'Token present' : 'No token' })

      // Check if token is available and valid
      if (!envToken || envToken === 'your_direct_line_token_here') {
        const errorMsg = 'Direct Line token not configured. Please set VITE_DIRECT_LINE_TOKEN in your .env file.'
        console.error('❌ Token validation failed:', errorMsg)
        setError(errorMsg)
        setIsInitializing(false)
        return
      }

      // Validate token format using utility function
      const tokenValidation = validateTokenFormat(envToken)
      if (!tokenValidation.isValid) {
        const errorMsg = `Invalid Direct Line token: ${tokenValidation.error}`
        const troubleshootingSteps = getTokenTroubleshootingSteps(tokenValidation.error || '')
        console.error('❌ Token format validation failed:', {
          error: errorMsg,
          troubleshooting: troubleshootingSteps,
          details: tokenValidation.details
        })
        setError(`${errorMsg}\n\nTroubleshooting steps:\n${troubleshootingSteps.join('\n')}`)
        setIsInitializing(false)
        return
      }

      try {
        console.log('🔗 Creating DirectLine connection...', {
          tokenLength: envToken.length,
          mode: isFloating ? 'floating' : 'fullpage'
        })

        // Create DirectLine connection with proper options
        const directLineOptions: DirectLineOptions = {
          token: envToken.trim(),
          pollingInterval: 1000,
        }

        console.log('🔧 DirectLine options:', {
          tokenLength: directLineOptions.token?.length,
          pollingInterval: directLineOptions.pollingInterval,
          mode: isFloating ? 'floating' : 'fullpage'
        })

        const dl = createDirectLine(directLineOptions)
        console.log('🔗 DirectLine instance created:', dl)

        // Set up connection status monitoring with proper typing
        dl.connectionStatus$.subscribe({
          next: (connectionStatus: number) => {
            const statusMap = {
              0: 'Uninitialized',
              1: 'Connecting',
              2: 'Online',
              3: 'ExpiredToken',
              4: 'FailedToConnect',
              5: 'Ended'
            }
            const statusName = statusMap[connectionStatus as keyof typeof statusMap] || 'Unknown'
            console.log(`🔄 Connection status: ${connectionStatus} (${statusName})`, { isFloating })
            setConnectionStatus(statusName)

            if (connectionStatus === 2) { // Online
              console.log('✅ WebChat connected successfully!', { mode: isFloating ? 'floating' : 'fullpage' })
              setError('') // Clear any previous errors
              setRetryCount(0) // Reset retry count on successful connection
            } else if (connectionStatus === 3) { // ExpiredToken
              const errorMsg = 'Direct Line token has expired. Please generate a new token.'
              const troubleshootingSteps = getTokenTroubleshootingSteps('expired')
              console.error('❌ Token expired:', errorMsg, { troubleshooting: troubleshootingSteps })
              setError(`${errorMsg}\n\nTroubleshooting steps:\n${troubleshootingSteps.join('\n')}`)
            } else if (connectionStatus === 4) { // FailedToConnect
              const errorMsg = 'Failed to connect to bot. Please check your Direct Line token and bot service.'
              const troubleshootingSteps = getTokenTroubleshootingSteps('404')
              console.error('❌ Connection failed:', errorMsg, { troubleshooting: troubleshootingSteps })
              setError(`${errorMsg}\n\nTroubleshooting steps:\n${troubleshootingSteps.join('\n')}`)
            } else if (connectionStatus === 5) { // Ended
              console.warn('⚠️ Connection ended')
            }
          },
          error: (connectionError: Error) => {
            console.error('❌ Connection error:', connectionError, { isFloating })
            setError(`Connection error: ${connectionError.message}`)
          }
        })

        setDirectLine(dl)
        setIsInitializing(false)

      } catch (initError) {
        console.error('❌ Error initializing WebChat:', initError)
        const errorMessage = initError instanceof Error ? initError.message : 'Unknown error occurred'
        const troubleshootingSteps = getTokenTroubleshootingSteps(errorMessage)
        setError(`Error initializing WebChat: ${errorMessage}\n\nTroubleshooting steps:\n${troubleshootingSteps.join('\n')}`)
        setIsInitializing(false)
        setConnectionStatus('Error')
      }
    }

    initializeWebChat()
  }, [envToken, isFloating])

  // Retry connection function
  const retryConnection = () => {
    setRetryCount(prev => prev + 1)
    setError('')
    setIsInitializing(true)
    setConnectionStatus('Retrying...')

    // Re-trigger the useEffect by updating a dependency
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  }

  // Create style set using the centralized theme system
  const styleSet = createStyleSet(createWebChatStyleSet())

  // Container styles using theme tokens
  const containerStyles: React.CSSProperties = {
    ...theme.components.webChat.container,
  }

  const configPanelStyles: React.CSSProperties = {
    ...theme.components.webChat.configPanel,
  }

  const chatWrapperStyles: React.CSSProperties = {
    ...theme.components.webChat.chatWrapper,
  }

  const chatStyles: React.CSSProperties = {
    ...theme.components.webChat.chat,
  }

  // Render loading state
  if (isInitializing) {
    return (
      <Card variant="elevated" style={containerStyles}>
        <div style={configPanelStyles}>
          <LoadingIndicator
            title="Initializing WebChat..."
            description={`Connecting to ${botName}`}
          />
        </div>
        <div style={chatWrapperStyles}>
          <div style={chatStyles}>
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: theme.colors.neutral600,
              padding: theme.spacing[5],
            }}>
              <p style={{
                margin: `0 0 ${theme.spacing[3]} 0`,
                fontSize: theme.fonts.sizes.lg,
                fontWeight: theme.fonts.weights.medium,
              }}>
                Initializing chat...
              </p>
              <small style={{
                fontSize: theme.fonts.sizes.sm,
                opacity: theme.opacity.hover,
              }}>
                Please wait while we connect to the bot
              </small>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  // Render error state
  if (error) {
    return (
      <Card variant="elevated" style={containerStyles}>
        <div style={configPanelStyles}>
          <ErrorIndicator
            title="Configuration Error"
            description="WebChat cannot be initialized"
          />
        </div>
        <div style={chatWrapperStyles}>
          <div style={chatStyles}>
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: theme.colors.error,
              padding: theme.spacing[5],
              maxWidth: '80%',
            }}>
              <p style={{
                margin: `0 0 ${theme.spacing[3]} 0`,
                fontSize: theme.fonts.sizes.lg,
                fontWeight: theme.fonts.weights.medium,
              }}>
                Configuration Error
              </p>
              <small style={{
                fontSize: theme.fonts.sizes.sm,
                lineHeight: theme.fonts.lineHeights.normal,
                display: 'block',
                wordBreak: 'break-word',
                marginBottom: theme.spacing[4],
              }}>
                {error}
              </small>
              <div style={{ marginTop: theme.spacing[4] }}>
                <button
                  onClick={retryConnection}
                  style={{
                    background: `linear-gradient(135deg, ${theme.colors.brandRed} 0%, ${theme.colors.brandOrange} 100%)`,
                    color: theme.colors.neutralWhite,
                    border: 'none',
                    padding: `${theme.spacing[2]} ${theme.spacing[4]}`,
                    borderRadius: '6px',
                    fontSize: theme.fonts.sizes.sm,
                    fontWeight: theme.fonts.weights.medium,
                    cursor: 'pointer',
                    transition: 'all 0.2s ease-in-out',
                    marginRight: theme.spacing[2],
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-1px)'
                    e.currentTarget.style.boxShadow = theme.shadows.medium
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                >
                  🔄 Retry Connection {retryCount > 0 && `(${retryCount})`}
                </button>
                <small style={{
                  fontSize: theme.fonts.sizes.xs,
                  color: theme.colors.neutral600,
                  display: 'block',
                  marginTop: theme.spacing[2],
                }}>
                  Status: {connectionStatus}
                </small>
              </div>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  // Render connection issue state
  if (!directLine) {
    return (
      <Card variant="elevated" style={containerStyles}>
        <div style={configPanelStyles}>
          <StatusIndicator
            status="warning"
            title="Connection Issue"
            description="DirectLine not available"
          />
        </div>
        <div style={chatWrapperStyles}>
          <div style={chatStyles}>
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: theme.colors.neutral600,
              padding: theme.spacing[5],
            }}>
              <p style={{
                margin: `0 0 ${theme.spacing[3]} 0`,
                fontSize: theme.fonts.sizes.lg,
                fontWeight: theme.fonts.weights.medium,
              }}>
                Connection Issue
              </p>
              <small style={{
                fontSize: theme.fonts.sizes.sm,
                opacity: theme.opacity.hover,
              }}>
                DirectLine connection not established
              </small>
            </div>
          </div>
        </div>
      </Card>
    )
  }

  // Main WebChat render - successful connection
  return (
    <Card variant="elevated" style={containerStyles}>
      {isFloating && onClose && (
        <div style={{
          position: 'absolute',
          top: theme.spacing[3],
          right: theme.spacing[3],
          zIndex: 1000,
        }}>
          <button
            onClick={onClose}
            style={{
              background: 'transparent',
              border: 'none',
              fontSize: '20px',
              cursor: 'pointer',
              color: theme.colors.neutral600,
              padding: theme.spacing[1],
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '32px',
              height: '32px',
              transition: 'all 0.2s ease-in-out',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = theme.colors.neutral100
              e.currentTarget.style.color = theme.colors.neutral800
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
              e.currentTarget.style.color = theme.colors.neutral600
            }}
            aria-label="Close chat"
            title="Close chat"
          >
            ×
          </button>
        </div>
      )}
      {!isFloating && (
        <div style={configPanelStyles}>
          <StatusIndicator
            status="success"
            title={`Connected to ${botName}`}
            description="Using environment token"
          />
        </div>
      )}
      <div style={chatWrapperStyles}>
        <div style={chatStyles}>
          {directLine ? (
            <ReactWebChat
              directLine={directLine}
              styleSet={styleSet}
              userID={`user-${Math.random().toString(36).substring(2, 11)}`}
              locale="en-US"
            />
          ) : (
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: theme.colors.neutral600,
              padding: theme.spacing[5],
            }}>
              <p style={{
                margin: `0 0 ${theme.spacing[3]} 0`,
                fontSize: theme.fonts.sizes.lg,
                fontWeight: theme.fonts.weights.medium,
              }}>
                Establishing Connection...
              </p>
              <small style={{
                fontSize: theme.fonts.sizes.sm,
                opacity: theme.opacity.hover,
              }}>
                Status: {connectionStatus}
              </small>
            </div>
          )}
        </div>
      </div>
    </Card>
  )
}

export default WebChatComponent