import React, { useState } from 'react'
import WebChatComponent from './components/WebChatComponent'
import { DemoPage } from './components/DemoPage'
import { TokenTester } from './components/TokenTester'
import { TroubleshootingGuide } from './components/TroubleshootingGuide'
import { theme } from './theme'
import './App.css'

/**
 * Main App component
 * Provides the layout structure for the Copilot Studio WebChat application
 * Now includes both full-page and floating chat modes
 */
const App: React.FC = () => {
  const [viewMode, setViewMode] = useState<'demo' | 'fullpage' | 'debug'>('demo')

  const toggleViewMode = () => {
    setViewMode(prev => {
      if (prev === 'demo') return 'fullpage'
      if (prev === 'fullpage') return 'debug'
      return 'demo'
    })
  }

  const switcherStyles: React.CSSProperties = {
    position: 'fixed',
    top: '20px',
    left: '20px',
    zIndex: 1001,
    background: `linear-gradient(135deg, ${theme.colors.brandRed} 0%, ${theme.colors.brandOrange} 100%)`,
    color: theme.colors.neutralWhite,
    border: 'none',
    padding: `${theme.spacing[2]} ${theme.spacing[4]}`,
    borderRadius: '8px',
    fontSize: theme.fonts.sizes.sm,
    fontWeight: theme.fonts.weights.medium,
    cursor: 'pointer',
    boxShadow: theme.shadows.medium,
    transition: 'all 0.2s ease-in-out',
  }

  if (viewMode === 'demo') {
    return (
      <>
        <button
          style={switcherStyles}
          onClick={toggleViewMode}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)'
            e.currentTarget.style.boxShadow = theme.shadows.large
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)'
            e.currentTarget.style.boxShadow = theme.shadows.medium
          }}
          title="Switch to full-page mode"
        >
          {viewMode === 'demo' ? '📄 Full Page Mode' : viewMode === 'fullpage' ? '🧪 Debug Mode' : '💬 Demo Mode'}
        </button>
        <DemoPage />
      </>
    )
  }

  if (viewMode === 'debug') {
    const envToken = import.meta.env.VITE_DIRECT_LINE_TOKEN
    return (
      <>
        <button
          style={switcherStyles}
          onClick={toggleViewMode}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)'
            e.currentTarget.style.boxShadow = theme.shadows.large
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)'
            e.currentTarget.style.boxShadow = theme.shadows.medium
          }}
          title="Switch to demo mode"
        >
          💬 Demo Mode
        </button>
        <div style={{ padding: '20px', fontFamily: theme.fonts.families.primary }}>
          <h1 style={{ color: theme.colors.brandRed, marginBottom: '20px' }}>
            🧪 Direct Line Token Debug Mode
          </h1>
          <p style={{ marginBottom: '20px', color: theme.colors.neutral700 }}>
            This debug mode helps diagnose connection issues with your Direct Line token.
          </p>
          {envToken ? (
            <>
              <TokenTester token={envToken} />
              <TroubleshootingGuide />
            </>
          ) : (
            <>
              <div style={{
                padding: '20px',
                backgroundColor: theme.colors.brandLightRed,
                borderRadius: '8px',
                border: `1px solid ${theme.colors.brandRed}`,
                color: theme.colors.brandDarkRed,
                marginBottom: '20px'
              }}>
                <strong>❌ No Direct Line token found</strong>
                <p>Please set VITE_DIRECT_LINE_TOKEN in your .env file.</p>
              </div>
              <TroubleshootingGuide />
            </>
          )}
        </div>
      </>
    )
  }

  return (
    <>
      <button
        style={switcherStyles}
        onClick={toggleViewMode}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateY(-2px)'
          e.currentTarget.style.boxShadow = theme.shadows.large
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateY(0)'
          e.currentTarget.style.boxShadow = theme.shadows.medium
        }}
        title={viewMode === 'fullpage' ? 'Switch to debug mode' : 'Switch to demo mode'}
      >
        {viewMode === 'fullpage' ? '🧪 Debug Mode' : '💬 Demo Mode'}
      </button>
      <div className="app">
        <header className="app-header">
          <h1>🤖 Copilot Studio Web Chat</h1>
          <p>Microsoft Bot Framework Integration POC - TypeScript Version</p>
        </header>

        <main className="app-main">
          <WebChatComponent />
        </main>

        <footer className="app-footer">
          <p>Built with React + TypeScript + Vite + Bot Framework Web Chat</p>
        </footer>
      </div>
    </>
  )
}

export default App
