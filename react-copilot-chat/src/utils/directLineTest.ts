/**
 * Direct Line Connection Test Utility
 * Tests Direct Line connectivity and helps diagnose connection issues
 */

export interface DirectLineTestResult {
  success: boolean
  error?: string
  details?: {
    conversationId?: string
    token?: string
    endpoint?: string
    statusCode?: number
    responseTime?: number
  }
}

/**
 * Tests Direct Line connection by creating a conversation
 */
export async function testDirectLineConnection(token: string): Promise<DirectLineTestResult> {
  const startTime = Date.now()
  
  try {
    console.log('🧪 Testing Direct Line connection...')
    
    const response = await fetch('https://directline.botframework.com/v3/directline/conversations', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })
    
    const responseTime = Date.now() - startTime
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ Direct Line connection test successful:', {
        conversationId: data.conversationId,
        responseTime: `${responseTime}ms`
      })
      
      return {
        success: true,
        details: {
          conversationId: data.conversationId,
          token: token.substring(0, 20) + '...',
          endpoint: 'https://directline.botframework.com/v3/directline',
          statusCode: response.status,
          responseTime
        }
      }
    } else {
      const errorText = await response.text()
      console.error('❌ Direct Line connection test failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
        responseTime: `${responseTime}ms`
      })
      
      return {
        success: false,
        error: `HTTP ${response.status}: ${errorText}`,
        details: {
          statusCode: response.status,
          responseTime
        }
      }
    }
  } catch (error) {
    const responseTime = Date.now() - startTime
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    
    console.error('❌ Direct Line connection test error:', {
      error: errorMessage,
      responseTime: `${responseTime}ms`
    })
    
    return {
      success: false,
      error: errorMessage,
      details: {
        responseTime
      }
    }
  }
}

/**
 * Tests WebSocket connectivity (separate from Direct Line)
 */
export function testWebSocketConnectivity(): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      // Test if WebSocket is available and working
      const testWs = new WebSocket('wss://echo.websocket.org/')
      
      testWs.onopen = () => {
        console.log('✅ WebSocket connectivity test passed')
        testWs.close()
        resolve(true)
      }
      
      testWs.onerror = (error) => {
        console.warn('⚠️ WebSocket connectivity test failed:', error)
        resolve(false)
      }
      
      testWs.onclose = () => {
        // Connection closed normally
      }
      
      // Timeout after 5 seconds
      setTimeout(() => {
        if (testWs.readyState === WebSocket.CONNECTING) {
          testWs.close()
          resolve(false)
        }
      }, 5000)
      
    } catch (error) {
      console.warn('⚠️ WebSocket not available:', error)
      resolve(false)
    }
  })
}

/**
 * Comprehensive connectivity test
 */
export async function runConnectivityTests(token: string): Promise<{
  directLine: DirectLineTestResult
  webSocket: boolean
  summary: string
}> {
  console.log('🔍 Running comprehensive connectivity tests...')
  
  const [directLineResult, webSocketResult] = await Promise.all([
    testDirectLineConnection(token),
    testWebSocketConnectivity()
  ])
  
  let summary = ''
  if (directLineResult.success && webSocketResult) {
    summary = '✅ All connectivity tests passed'
  } else if (directLineResult.success && !webSocketResult) {
    summary = '⚠️ Direct Line works, WebSocket issues detected (may not affect bot functionality)'
  } else if (!directLineResult.success && webSocketResult) {
    summary = '❌ Direct Line connection failed, check token and bot configuration'
  } else {
    summary = '❌ Multiple connectivity issues detected'
  }
  
  console.log('📊 Connectivity test summary:', summary)
  
  return {
    directLine: directLineResult,
    webSocket: webSocketResult,
    summary
  }
}
